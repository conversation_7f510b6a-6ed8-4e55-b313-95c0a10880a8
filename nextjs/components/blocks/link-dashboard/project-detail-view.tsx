"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import { 
  ArrowLeft,
  Plus,
  RefreshCw,
  ExternalLink,
  Globe,
  Search,
  Filter,
  BarChart3,
  TrendingUp,
  Link2,
  Eye,
  Activity,
  Lock,
  Calendar,
  Clock,
  Shield,
  Zap
} from "lucide-react";
import MetricsCharts from "./metrics-charts";
import EnhancedBacklinksTable from "./enhanced-backlinks-table";
import { GroupedBacklinksTable } from "./grouped-backlinks-table";
import { Project, Link, DiscoveredLinkWithStats, ProjectStats } from "@/types/links";
import { toast } from "sonner";
import { CsvImportDialog } from "./csv-import-dialog";
import { AnalyticsConfigDialog } from "./analytics-config-dialog";
import { ProjectInfoTab } from "./project-info-tab";
import { normalizeUrl } from "@/utils/url-normalization";
import { useTierStatus } from "@/lib/hooks/useTierStatus";

interface ProjectDetailViewProps {
  project: Project;
  onBack: () => void;
  onEditLink: (link: Link) => void;
  onDeleteLink: (link: Link) => void;
  onAddLink: () => void;
  onProjectUpdate?: (project: Project) => void;
  onSidebarNavigation?: (section: string) => void;
}

export function ProjectDetailView({
  project,
  onBack,
  onEditLink,
  onDeleteLink,
  onAddLink,
  onProjectUpdate
}: ProjectDetailViewProps) {
  const { isPaidUser, canMakeTrafficUpdate } = useTierStatus();
  const [activeTab, setActiveTab] = useState("analytics");
  const [userLinks, setUserLinks] = useState<Link[]>([]);
  const [linkResources, setLinkResources] = useState<Link[]>([]);
  const [discoveredLinks, setDiscoveredLinks] = useState<DiscoveredLinkWithStats[]>([]);
  const [projectStats, setProjectStats] = useState<ProjectStats[]>([]);
  const [updating, setUpdating] = useState(false);
  const [scanning, setScanning] = useState(false);
  const [updatingTraffic, setUpdatingTraffic] = useState(false);
  const [viewMode, setViewMode] = useState<"detailed" | "grouped">("detailed");
  const [analyticsConfigs, setAnalyticsConfigs] = useState<any[]>([]);
  const [aiTrafficData, setAiTrafficData] = useState<{
    chatgpt_user: number;
    claude_user: number;
    claude_searchbot: number;
    perplexity_user: number;
    other: number;
    loading: boolean;
  }>({ chatgpt_user: 0, claude_user: 0, claude_searchbot: 0, perplexity_user: 0, other: 0, loading: true });

  useEffect(() => {
    fetchProjectData();
    fetchAiTrafficData();
  }, [project.id]);

  const fetchProjectData = async () => {
    await Promise.all([
      fetchProjectLinks(),
      fetchLinkResources(),
      fetchDiscoveredLinks(),
      fetchProjectStats(),
      fetchAnalyticsConfigs()
    ]);
  };

  const fetchLinkResources = async () => {
    try {
      const response = await fetch('/api/links');
      if (response.ok) {
        const data = await response.json();
        setLinkResources(data.links || []);
        console.log("Link resources fetched:", data.links);
      }
    } catch (error) {
      console.error("Error fetching link resources:", error);
    }
  };

  const fetchProjectLinks = async () => {
    try {
      const response = await fetch(`/api/projects/${project.id}/links`);
      if (response.ok) {
        const data = await response.json();
        setUserLinks(data.links || []);
        console.log("User links fetched:", data.links);
      }
    } catch (error) {
      console.error("Error fetching project links:", error);
      toast.error("Failed to fetch project links");
    }
  };

  const fetchDiscoveredLinks = async () => {
    try {
      const response = await fetch(`/api/projects/${project.id}/discovered-links`);
      if (response.ok) {
        const data = await response.json();
        setDiscoveredLinks(data.links || []);
        console.log("Discovered links fetched:", data.links);
      }
    } catch (error) {
      console.error("Error fetching discovered links:", error);
    }
  };

  const fetchProjectStats = async () => {
    try {
      const response = await fetch(`/api/projects/${project.id}/stats`);
      if (response.ok) {
        const data = await response.json();
        setProjectStats(data.stats || []);
        console.log("Project stats fetched:", data.stats);
      }
    } catch (error) {
      console.error("Error fetching project stats:", error);
    }
  };

  const fetchAnalyticsConfigs = async () => {
    try {
      const response = await fetch(`/api/projects/${project.id}/analytics-config`);
      if (response.ok) {
        const data = await response.json();
        setAnalyticsConfigs(data.configs || []);
        console.log("Analytics configs fetched:", data.configs);
      }
    } catch (error) {
      console.error("Error fetching analytics configs:", error);
    }
  };


  const fetchAiTrafficData = async () => {
    try {
      setAiTrafficData(prev => ({ ...prev, loading: true }));
      
      const response = await fetch(`/api/analytics/ai-traffic?domain=${encodeURIComponent(project.domain)}&projectId=${project.id}`);
      
      if (response.ok) {
        const data = await response.json();
        setAiTrafficData({
          chatgpt_user: data.sources?.chatgpt_user || 0,
          claude_user: data.sources?.claude_user || 0,
          claude_searchbot: data.sources?.claude_searchbot || 0,
          perplexity_user: data.sources?.perplexity_user || 0,
          other: data.sources?.other || 0,
          loading: false
        });
      } else {
        const error = await response.json();
        console.error("AI traffic API error:", error);
        setAiTrafficData({
          chatgpt_user: 0,
          claude_user: 0,
          claude_searchbot: 0,
          perplexity_user: 0,
          other: 0,
          loading: false
        });
      }
    } catch (error) {
      console.error("Error fetching AI traffic data:", error);
      setAiTrafficData({
        chatgpt_user: 0,
        claude_user: 0,
        claude_searchbot: 0,
        perplexity_user: 0,
        other: 0,
        loading: false
      });
    }
  };

  const handleRefreshStats = async () => {
    setUpdating(true);
    try {
      const response = await fetch(`/api/projects/${project.id}/stats`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ action: 'refresh' }),
      });

      const result = await response.json();

      if (response.ok) {
        toast.success(result.message || "Stats updated successfully");
        await fetchProjectStats();
        console.log("Stats updated successfully");
      } else {
        toast.error(result.error || "Failed to update stats");
      }
    } catch (error) {
      console.error("Error updating stats:", error);
      toast.error("Failed to update stats");
    } finally {
      setUpdating(false);
    }
  };

  const handleScanForLinks = async () => {
    if (!isPaidUser) {
      toast.error("Link scanning is available for Professional users only. Please upgrade to access this feature.");
      return;
    }

    setScanning(true);
    try {
      const response = await fetch(`/api/projects/${project.id}/scan-links`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
      });

      const result = await response.json();

      if (response.ok) {
        toast.success(`Scan completed! Found ${result.newLinks || 0} new links`);
        fetchDiscoveredLinks();
        console.log("Scan completed! Found", result.newLinks, "new links");
      } else {
        toast.error(result.error || "Failed to scan for links");
      }
    } catch (error) {
      console.error("Error scanning for links:", error);
      toast.error("Failed to scan for links");
    } finally {
      setScanning(false);
    }
  };

  const handleSyncExternalLinks = async () => {
    setUpdating(true);
    try {
      const response = await fetch(`/api/projects/${project.id}/sync-external-links`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
      });

      const result = await response.json();

      if (response.ok) {
        toast.success(`Sync completed! Added ${result.newLinks || 0} promotional platforms from Link Resources`);
        fetchDiscoveredLinks();
        console.log("Sync completed! Added", result.newLinks, "promotional platforms from Link Resources");
      } else {
        toast.error(result.error || "Failed to sync promotional platforms");
      }
    } catch (error) {
      console.error("Error syncing promotional platforms:", error);
      toast.error("Failed to sync promotional platforms");
    } finally {
      setUpdating(false);
    }
  };

  const handleUpdateTrafficContribution = async () => {
    if (!canMakeTrafficUpdate) {
      toast.error("Traffic updates are available for Professional users only. Please upgrade to access this feature.");
      return;
    }

    setUpdatingTraffic(true);
    try {
      // Get active analytics configs for this project
      const activeConfig = analyticsConfigs.find(config => config.isActive);
      
      if (!activeConfig) {
        toast.error("No active analytics configuration found. Please configure an analytics platform first.");
        return;
      }

      const analyticsConfig = {
        platform: activeConfig.provider,
        domain: project.domain,
        api_key: activeConfig.configData?.api_key,
        website_id: activeConfig.configData?.website_id,
        base_url: activeConfig.configData?.base_url,
      };

      const response = await fetch(`/api/projects/${project.id}/discovered-links/update-traffic`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({
          analytics_config: analyticsConfig,
          period: 'last_30_days'
        }),
      });

      const result = await response.json();

      if (response.ok) {
        toast.success(`Referral traffic data updated! Updated ${result.results?.updated || 0} discovered links from ${analyticsConfig.platform}`);
        fetchDiscoveredLinks();
      } else {
        toast.error(result.error || "Failed to update referral traffic data for discovered links");
      }
    } catch (error) {
      console.error("Error updating referral traffic for discovered links:", error);
      toast.error("Failed to update referral traffic data for discovered links");
    } finally {
      setUpdatingTraffic(false);
    }
  };

  const handleImportDiscoveredLink = async (discoveredLink: DiscoveredLinkWithStats) => {
    try {
      const response = await fetch("/api/links", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          url: normalizeUrl(discoveredLink.url).normalized,
          title: discoveredLink.title,
          link_type: "free",
          source: "Discovered",
          acquisition_method: "Automatic Discovery",
          notes: `Imported from discovered links. Source: ${discoveredLink.source_url}`,
        }),
      });

      if (response.ok) {
        toast.success("Link imported successfully");
        fetchProjectLinks();
      } else {
        const error = await response.json();
        toast.error(error.error || "Failed to import link");
      }
    } catch (error) {
      console.error("Error importing link:", error);
      toast.error("Failed to import link");
    }
  };

  const calculateCurrentStats = () => {
    const totalUserLinks = userLinks.length;
    const indexedUserLinks = userLinks.filter(link => link.is_indexed).length;
    const totalDiscoveredLinks = discoveredLinks.filter(link => link.status !== 'ARCHIVED').length;
    const indexedDiscoveredLinks = discoveredLinks.filter(link => link.status === 'INDEXED').length;
    
    const avgDrScore = userLinks.length > 0 
      ? Math.round(userLinks.reduce((sum, link) => sum + (link.dr_score || 0), 0) / userLinks.length)
      : 0;
      
    const monthlyTraffic = userLinks.reduce((sum, link) => sum + link.traffic, 0);

    return {
      totalUserLinks,
      indexedUserLinks,
      totalDiscoveredLinks,
      indexedDiscoveredLinks,
      avgDrScore,
      monthlyTraffic
    };
  };

  const stats = calculateCurrentStats();


  const getTotalAiTraffic = () => {
    return aiTrafficData.chatgpt_user + aiTrafficData.claude_user + aiTrafficData.claude_searchbot + 
           aiTrafficData.perplexity_user + aiTrafficData.other;
  };

  const renderAiTrafficCard = () => {
    const totalTraffic = getTotalAiTraffic();
    
    const aiSources = [
      { name: 'ChatGPT-User', value: aiTrafficData.chatgpt_user, color: 'from-green-400 to-green-500', bgColor: 'hsl(var(--ai-chatgpt))', icon: '🤖', description: 'ChatGPT曝光' },
      { name: 'Claude-User', value: aiTrafficData.claude_user, color: 'from-violet-400 to-violet-500', bgColor: 'hsl(var(--ai-claude))', icon: '🧠', description: 'Claude用户曝光' },
      { name: 'Claude-SearchBot', value: aiTrafficData.claude_searchbot, color: 'from-blue-400 to-blue-500', bgColor: 'hsl(var(--ai-claude-search))', icon: '🔍', description: 'Claude搜索机器人曝光' },
      { name: 'Perplexity-User', value: aiTrafficData.perplexity_user, color: 'from-cyan-400 to-cyan-500', bgColor: 'hsl(var(--ai-perplexity))', icon: '🎯', description: 'Perplexity AI曝光' },
      { name: 'Other', value: aiTrafficData.other, color: 'from-slate-400 to-slate-500', bgColor: 'hsl(var(--ai-other))', icon: '🔧', description: '其他AI来源' }
    ].sort((a, b) => b.value - a.value);

    return (
      <Card className="card-glow border-0 w-full">
        <CardHeader className="pb-3">
          <CardTitle className="text-lg font-semibold flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Zap className="w-5 h-5 text-primary animate-pulse" />
              <span className="text-matrix">AI 流量分析</span>
            </div>
            <div className="text-right">
              <div className="text-2xl font-bold text-primary">{totalTraffic.toLocaleString()}</div>
              <div className="text-sm text-muted-foreground">总 AI 流量</div>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {aiTrafficData.loading ? (
            <div className="animate-pulse space-y-3">
              {[1, 2, 3, 4, 5].map(i => (
                <div key={i} className="flex items-center space-x-3">
                  <div className="h-4 w-4 bg-gray-200 rounded"></div>
                  <div className="h-4 bg-gray-200 rounded flex-1"></div>
                  <div className="h-4 w-16 bg-gray-200 rounded"></div>
                </div>
              ))}
            </div>
          ) : (
            <div className="grid gap-4">
              {aiSources.map((source, index) => {
                const percentage = totalTraffic > 0 ? (source.value / totalTraffic * 100) : 0;
                return (
                  <div key={source.name} className="space-y-2">
                    <div className="flex items-center justify-between text-sm">
                      <div className="flex items-center gap-3">
                        <span className="text-lg">{source.icon}</span>
                        <div>
                          <span className="font-medium">{source.name}</span>
                          <div className="text-xs text-muted-foreground">{source.description}</div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="font-semibold">{source.value.toLocaleString()}</div>
                        <div className="text-xs text-muted-foreground">{percentage.toFixed(1)}%</div>
                      </div>
                    </div>
                    <div className="relative h-3 bg-muted rounded-full overflow-hidden">
                      <div
                        className="h-full transition-all duration-700 ease-out"
                        style={{
                          width: `${percentage}%`,
                          backgroundColor: source.bgColor,
                          opacity: 0.8
                        }}
                      />
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </CardContent>
      </Card>
    );
  };

  const renderAnalyticsTab = () => (
    <div className="w-full max-w-full overflow-hidden space-y-6">
      {/* Stats Header */}
      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4 px-2 sm:px-0">
        <h3 className="text-2xl font-bold text-matrix">
          项目分析面板
        </h3>
      </div>

      {/* AI Traffic Card - Increased width */}
      {/* <div className="w-full px-2 sm:px-0">
        {renderAiTrafficCard()}
      </div> */}

      {/* Metrics Charts */}
      <div className="w-full max-w-full overflow-hidden">
        <MetricsCharts
          project_id={project.id}
          projectName={project.name}
          domain={project.domain}
          currentDR={stats.avgDrScore}
          currentLinks={stats.totalUserLinks + stats.indexedDiscoveredLinks}
          currentPageViews={stats.monthlyTraffic}
          statsData={projectStats}
          onRefreshStats={handleRefreshStats}
          onConfigsUpdate={setAnalyticsConfigs}
        />
      </div>
    </div>
  );

  const renderBacklinksTab = () => (
    <div className="w-full max-w-full overflow-hidden space-y-4 lg:space-y-6">
      {/* Backlinks Header */}
      <div className="flex flex-col lg:flex-row items-start lg:items-center justify-between gap-4 px-2 sm:px-0">
        <div className="min-w-0 flex-1">
          <h3 className="text-lg font-semibold">External Link</h3>
        </div>
        <div className="flex flex-col sm:flex-row gap-2 w-full lg:w-auto max-w-full overflow-x-auto">
          <Button 
            onClick={handleScanForLinks} 
            disabled={scanning || !isPaidUser}
            variant="outline"
            size="sm"
            className="justify-center whitespace-nowrap"
            title={!isPaidUser ? "Link scanning is available for Professional users only" : "Scan for external links"}
          >
            {!isPaidUser && <Lock className="h-4 w-4 mr-2" />}
            {isPaidUser && <Search className={`h-4 w-4 mr-2 ${scanning ? 'animate-spin' : ''}`} />}
            <span className="hidden sm:inline">{!isPaidUser ? "Scan (Pro)" : "Scan"}</span>
            <span className="sm:hidden">{!isPaidUser ? "Pro Only" : "Scan"}</span>
          </Button>
          <CsvImportDialog projectId={project.id} onImportComplete={() => {
            toast.success("CSV imported successfully");
            fetchDiscoveredLinks();
          }} />
          <Button 
            onClick={handleSyncExternalLinks} 
            disabled={updating}
            variant="outline"
            size="sm"
            className="justify-center whitespace-nowrap"
            title="Sync promotional platforms from Link Resources as external link opportunities"
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${updating ? 'animate-spin' : ''}`} />
            <span className="hidden sm:inline">Sync Upstream</span>
            <span className="sm:hidden">Sync</span>
          </Button>
          <Button 
            onClick={handleUpdateTrafficContribution} 
            disabled={updatingTraffic || !canMakeTrafficUpdate}
            variant="outline"
            size="sm"
            className="justify-center whitespace-nowrap"
            title={!canMakeTrafficUpdate ? "Traffic updates are available for Professional users only" : "Update referral traffic data for discovered links from analytics platforms (Plausible, Google Analytics, etc.)"}
          >
            {!canMakeTrafficUpdate && <Lock className="h-4 w-4 mr-2" />}
            {canMakeTrafficUpdate && <Activity className={`h-4 w-4 mr-2 ${updatingTraffic ? 'animate-spin' : ''}`} />}
            <span className="hidden sm:inline">{!canMakeTrafficUpdate ? "Update Traffic (Pro)" : "Update Traffic"}</span>
            <span className="sm:hidden">{!canMakeTrafficUpdate ? "Pro Only" : "Traffic"}</span>
          </Button>
          <Button onClick={onAddLink} size="sm" className="justify-center whitespace-nowrap">
            <Plus className="h-4 w-4 mr-2" />
            Add Link
          </Button>
        </div>
      </div>

      {/* Discovered Links Stats */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 px-2 sm:px-0">
        <Card className="card-glow border-0 hover:shadow-lg transition-all duration-300">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-2xl font-bold text-primary">{stats.totalDiscoveredLinks}</p>
                <p className="text-sm text-muted-foreground">Total links</p>
              </div>
              <div className="p-2 rounded-lg" style={{ backgroundColor: 'hsl(var(--dashboard-accent-2) / 0.15)' }}>
                <Search className="h-6 w-6" style={{ color: 'hsl(var(--dashboard-accent-2))' }} />
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card className="card-glow border-0 hover:shadow-lg transition-all duration-300">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-2xl font-bold text-primary">{stats.indexedDiscoveredLinks}</p>
                <p className="text-sm text-muted-foreground">Indexed Links</p>
              </div>
              <div className="p-2 rounded-lg" style={{ backgroundColor: 'hsl(var(--data-positive) / 0.15)' }}>
                <TrendingUp className="h-6 w-6" style={{ color: 'hsl(var(--data-positive))' }} />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* View Toggle */}
      <div className="flex items-center gap-2 mb-4 px-2 sm:px-0">
        <span className="text-sm font-medium">View Mode:</span>
        <div className="flex border border-border rounded-lg p-1">
          <Button
            variant={viewMode === "detailed" ? "default" : "ghost"}
            size="sm"
            onClick={() => setViewMode("detailed")}
            className="text-xs whitespace-nowrap"
          >
            Detailed View
          </Button>
          <Button
            variant={viewMode === "grouped" ? "default" : "ghost"}
            size="sm"
            onClick={() => setViewMode("grouped")}
            className="text-xs whitespace-nowrap"
          >
            Grouped by Source
          </Button>
        </div>
      </div>

      {/* Conditional Table Rendering */}
      <div className="w-full max-w-full overflow-hidden px-2 sm:px-0">
        {viewMode === "detailed" ? (
          <div className="w-full max-w-full overflow-x-auto">
            <EnhancedBacklinksTable
          discoveredLinks={discoveredLinks.map(link => ({
            id: link.id,
            url: link.url,
            title: link.title,
            domain: link.domain,
            anchorText: link.anchor_text,
            link_type: link.link_type,
            discoveredAt: link.discovered_at,
            sourceUrl: link.source_url,
            isActive: link.is_active,
            status: link.status,
            dr_score: link.dr_score,
            traffic: link.traffic,
            isInManagedList: linkResources.some(resource => normalizeUrl(resource.url).normalized === normalizeUrl(link.url).normalized) || link.url === link.source_url, // Hide "Add" button for links synced from link_resources (url === source_url indicates sync origin)
            // Traffic contribution from analytics platforms
            referral_traffic: (link as any).referral_traffic,
            analytics_source: (link as any).analytics_source,
            traffic_period: (link as any).traffic_period,
            last_traffic_update: (link as any).last_traffic_update,
            traffic_contribution_percentage: (link as any).traffic_contribution_percentage
          }))}
          onAddToManagedLinks={async (linkId: string) => {
            const link = discoveredLinks.find(l => l.id === linkId);
            if (link) {
              await handleImportDiscoveredLink(link);
              await fetchLinkResources();
            }
          }}
          onScanForLinks={handleScanForLinks}
          isScanning={scanning}
          onDeleteLink={async (linkId: string) => {
            try {
              const response = await fetch(`/api/projects/${project.id}/discovered-links/${linkId}`, {
                method: 'DELETE',
              });
              
              if (response.ok) {
                toast.success('Discovery link deleted successfully');
                await fetchDiscoveredLinks();
              } else {
                const error = await response.json();
                toast.error(error.error || 'Failed to delete discovery link');
              }
            } catch (error) {
              console.error('Error deleting discovery link:', error);
              toast.error('Failed to delete discovery link');
            }
          }}
          onUpdateLinkStatus={async (linkId: string, status: 'NEW' | 'SUBMITTED' | 'INDEXED' | 'ARCHIVED') => {
            try {
              const response = await fetch(`/api/projects/${project.id}/discovered-links/${linkId}`, {
                method: 'PATCH',
                headers: {
                  'Content-Type': 'application/json',
                },
                body: JSON.stringify({ status }),
              });
              
              if (response.ok) {
                toast.success('Link status updated successfully');
                await fetchDiscoveredLinks();
              } else {
                const error = await response.json();
                toast.error(error.error || 'Failed to update link status');
              }
            } catch (error) {
              console.error('Error updating link status:', error);
              toast.error('Failed to update link status');
            }
          }}
            />
          </div>
        ) : (
          <div className="w-full max-w-full overflow-x-auto">
            <GroupedBacklinksTable
          discoveredLinks={discoveredLinks.map(link => ({
            ...link,
            isInManagedList: linkResources.some(resource => normalizeUrl(resource.url).normalized === normalizeUrl(link.url).normalized) || link.url === link.source_url // Hide "Add" button for links synced from link_resources
          }))}
          onAddToManagedLinks={async (linkId: string) => {
            const link = discoveredLinks.find(l => l.id === linkId);
            if (link) {
              await handleImportDiscoveredLink(link);
              await fetchLinkResources();
            }
          }}
          onScanForLinks={handleScanForLinks}
          isScanning={scanning}
          onDeleteLink={async (linkId: string) => {
            try {
              const response = await fetch(`/api/projects/${project.id}/discovered-links/${linkId}`, {
                method: 'DELETE',
              });
              
              if (response.ok) {
                toast.success('Discovery link deleted successfully');
                await fetchDiscoveredLinks();
              } else {
                const error = await response.json();
                toast.error(error.error || 'Failed to delete discovery link');
              }
            } catch (error) {
              console.error('Error deleting discovery link:', error);
              toast.error('Failed to delete discovery link');
            }
          }}
          onUpdateLinkStatus={async (linkId: string, status: 'NEW' | 'SUBMITTED' | 'INDEXED' | 'ARCHIVED') => {
            try {
              const response = await fetch(`/api/projects/${project.id}/discovered-links/${linkId}`, {
                method: 'PATCH',
                headers: {
                  'Content-Type': 'application/json',
                },
                body: JSON.stringify({ status }),
              });
              
              if (response.ok) {
                toast.success('Link status updated successfully');
                await fetchDiscoveredLinks();
              } else {
                const error = await response.json();
                toast.error(error.error || 'Failed to update link status');
              }
            } catch (error) {
              console.error('Error updating link status:', error);
              toast.error('Failed to update link status');
            }
          }}
            />
          </div>
        )}
      </div>
    </div>
  );

  return (
    <div className="w-full max-w-full overflow-hidden space-y-4 lg:space-y-6">
      {/* Enhanced Header */}
      <div className="relative overflow-hidden">
        <div className="relative p-6 rounded-2xl card-glow backdrop-blur-sm border border-border/50">
            <div className="flex flex-col sm:flex-row items-start sm:items-center gap-6">
              <Button 
              variant="ghost" 
              size="sm" 
              onClick={onBack} 
              className="self-start sm:self-auto hover:bg-primary/10 transition-all duration-300 group border border-border/50 hover:border-primary/50"
            >
              <ArrowLeft className="h-4 w-4 mr-2 group-hover:-translate-x-1 transition-transform duration-300 text-primary" />
              <span className="hidden sm:inline">返回项目列表</span>
              <span className="sm:hidden">返回</span>
            </Button>
            
            <div className="flex-1 min-w-0 w-full">
              <div className="flex items-center gap-4">
                <div className="p-3 bg-gradient-to-r from-primary to-blue-500 rounded-xl shadow-lg animate-pulse-neon">
                  <Globe className="h-6 w-6 sm:h-8 sm:w-8 text-white" />
                </div>
                <div className="min-w-0 flex-1">
                  <h1 className="text-2xl sm:text-3xl font-bold text-matrix truncate">
                    {project.name}
                  </h1>
                  <div className="flex items-center gap-2 mt-2">
                    <Badge variant="outline" className="text-primary border-primary/50 bg-primary/10">
                      {project.domain}
                    </Badge>
                    <Badge variant="secondary" className="text-green-400 bg-green-500/20 border border-green-500/50">
                      活跃项目
                    </Badge>
                  </div>
                </div>
              </div>
            </div>
            
            {/* Quick Stats */}
            <div className="grid grid-cols-3 gap-4 sm:gap-6 w-full sm:w-auto">
              <div className="text-center">
                <div className="text-xl sm:text-2xl font-bold text-primary">{stats.avgDrScore}</div>
                <div className="text-xs text-muted-foreground">DR 评分</div>
              </div>
              <div className="text-center">
                <div className="text-xl sm:text-2xl font-bold text-blue-400">{stats.totalDiscoveredLinks}</div>
                <div className="text-xs text-muted-foreground">管理链接</div>
              </div>
              <div className="text-center">
                <div className="text-xl sm:text-2xl font-bold text-green-400">{stats.monthlyTraffic.toLocaleString()}</div>
                <div className="text-xs text-muted-foreground">月流量</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Enhanced Tabs */}
      <div className="w-full max-w-full overflow-hidden px-2 sm:px-0">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-3 h-auto p-2 bg-card border border-border rounded-2xl shadow-lg">
            <TabsTrigger 
              value="analytics" 
              className="flex flex-col sm:flex-row items-center gap-2 py-3 px-4 text-sm font-semibold min-w-0 rounded-xl transition-all duration-300 hover:bg-muted data-[state=active]:bg-primary data-[state=active]:text-primary-foreground data-[state=active]:border-2 data-[state=active]:border-primary data-[state=active]:shadow-lg dark:data-[state=active]:text-white"
            >
              <BarChart3 className="h-4 w-4 flex-shrink-0" />
              <span className="hidden sm:inline">数据分析</span>
              <span className="sm:hidden text-xs">分析</span>
            </TabsTrigger>
            <TabsTrigger 
              value="backlinks" 
              className="flex flex-col sm:flex-row items-center gap-2 py-3 px-4 text-sm font-semibold min-w-0 rounded-xl transition-all duration-300 hover:bg-muted data-[state=active]:bg-primary data-[state=active]:text-primary-foreground data-[state=active]:border-2 data-[state=active]:border-primary data-[state=active]:shadow-lg dark:data-[state=active]:text-white"
            >
              <Link2 className="h-4 w-4 flex-shrink-0" />
              <span className="hidden sm:inline">外部链接</span>
              <span className="sm:hidden text-xs">链接</span>
            </TabsTrigger>
            <TabsTrigger 
              value="info" 
              className="flex flex-col sm:flex-row items-center gap-2 py-3 px-4 text-sm font-semibold min-w-0 rounded-xl transition-all duration-300 hover:bg-muted data-[state=active]:bg-primary data-[state=active]:text-primary-foreground data-[state=active]:border-2 data-[state=active]:border-primary data-[state=active]:shadow-lg dark:data-[state=active]:text-white"
            >
              <Globe className="h-4 w-4 flex-shrink-0" />
              <span className="hidden sm:inline">项目信息</span>
              <span className="sm:hidden text-xs">信息</span>
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value="info" className="mt-4 lg:mt-6 w-full max-w-full overflow-hidden">
            <ProjectInfoTab
              project={project}
              onProjectUpdate={onProjectUpdate}
            />
          </TabsContent>

          <TabsContent value="analytics" className="mt-4 lg:mt-6 w-full max-w-full overflow-hidden">
            {renderAnalyticsTab()}
          </TabsContent>
          
          <TabsContent value="backlinks" className="mt-4 lg:mt-6 w-full max-w-full overflow-hidden">
            {renderBacklinksTab()}
          </TabsContent>
          
        </Tabs>
      </div>
    </div>
  );
} 
